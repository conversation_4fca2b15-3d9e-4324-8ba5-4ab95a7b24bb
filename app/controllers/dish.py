from fastapi import HTTPException
from tortoise.expressions import Q
from tortoise.transactions import atomic

from app.core.crud import CRUDBase
from app.controllers.utils import get_school_id
from app.models.dish import Dish
from app.models.food_stuff import FoodStuff, Unit
from app.schemas.dish import DishCreate, DishUpdate, DishSearch


class DishController(CRUDBase[Dish, DishCreate, DishUpdate]):
    def __init__(self):
        super().__init__(model=Dish)

    async def get_list(self, search: DishSearch):
        q = Q()
        school_id = await get_school_id()
        q &= Q(school_id=school_id)
        
        if search.dish_name:
            q &= Q(dish_name__contains=search.dish_name)
        if search.is_active is not None:
            q &= Q(is_active=search.is_active)

        # 分页查询
        total = await self.model.filter(q).count()
        offset = (search.current - 1) * search.size
        dishes = await self.model.filter(q).offset(offset).limit(search.size).order_by("-created_at")

        # 转换为字典格式
        result_items = []
        for dish in dishes:
            dish_dict = await dish.to_dict()
            result_items.append(dish_dict)

        return {
            "total": total,
            "items": result_items,
            "page": search.current,
            "page_size": search.size
        }

    async def validate_ingredients(self, ingredients: list):
        """验证食材列表的有效性"""
        for ingredient in ingredients:
            # 验证食材是否存在
            food_stuff = await FoodStuff.get_or_none(id=ingredient.food_stuff_id)
            if not food_stuff:
                raise HTTPException(status_code=400, detail=f"食材ID {ingredient.food_stuff_id} 不存在")
            
            # 验证单位是否存在
            unit = await Unit.get_or_none(id=food_stuff.food_stuff_unit_id)
            if not unit:
                raise HTTPException(status_code=400, detail=f"食材 {food_stuff.food_stuff_name} 的单位不存在")
            
            # 更新食材名称和单位名称（确保数据一致性）
            ingredient.food_stuff_name = food_stuff.food_stuff_name
            ingredient.unit_name = unit.unit_name

    @atomic()
    async def create_dish(self, obj_in: DishCreate):
        school_id = await get_school_id()
        
        # 检查菜品名称是否已存在
        existing_dish = await self.model.filter(dish_name=obj_in.dish_name, school_id=school_id).exists()
        if existing_dish:
            raise HTTPException(status_code=400, detail="菜品名称已存在")
        
        # 验证食材列表
        await self.validate_ingredients(obj_in.ingredients)
        
        # 转换食材列表为JSON格式
        ingredients_json = [ingredient.model_dump() for ingredient in obj_in.ingredients]
        
        # 创建菜品
        dish_data = obj_in.model_dump(exclude={"ingredients"})
        dish_data["ingredients"] = ingredients_json
        dish_data["school_id"] = school_id
        
        return await self.model.create(**dish_data)

    @atomic()
    async def update_dish(self, obj_in: DishUpdate):
        school_id = await get_school_id()
        
        # 获取现有菜品
        dish = await self.get(id=obj_in.id, school_id=school_id)
        if not dish:
            raise HTTPException(status_code=404, detail="菜品不存在")
        
        # 如果更新了菜品名称，检查是否重复
        if obj_in.dish_name and obj_in.dish_name != dish.dish_name:
            existing_dish = await self.model.filter(
                dish_name=obj_in.dish_name, 
                school_id=school_id
            ).exclude(id=obj_in.id).exists()
            if existing_dish:
                raise HTTPException(status_code=400, detail="菜品名称已存在")
        
        # 验证食材列表（如果提供了）
        if obj_in.ingredients:
            await self.validate_ingredients(obj_in.ingredients)
            ingredients_json = [ingredient.model_dump() for ingredient in obj_in.ingredients]
        else:
            ingredients_json = dish.ingredients
        
        # 更新菜品
        update_data = obj_in.update_dict()
        if obj_in.ingredients:
            update_data["ingredients"] = ingredients_json
        
        dish.update_from_dict(update_data)
        await dish.save()
        return dish

    @atomic()
    async def delete_dish(self, dish_id: int):
        school_id = await get_school_id()
        dish = await self.get(id=dish_id, school_id=school_id)
        if not dish:
            raise HTTPException(status_code=404, detail="菜品不存在")
        
        await dish.delete()
        return True


dish_controller = DishController()
